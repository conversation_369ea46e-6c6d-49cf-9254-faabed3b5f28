{"name": "create-base-project", "version": "1.0.0", "description": "CLI para criar projetos SvelteKit com PocketBase, Tailwind e shadcn-svelte", "type": "module", "main": "src/index.js", "bin": {"create-base-project": "src/index.js"}, "scripts": {"dev": "node src/index.js test-project", "build": "echo 'No build needed'"}, "keywords": ["sveltekit", "pocketbase", "shadcn", "tailwind", "cli", "scaffolding"], "author": "Ta<PERSON><PERSON><PERSON>", "license": "MIT", "dependencies": {"chalk": "^5.3.0", "commander": "^11.1.0", "execa": "^8.0.1", "fs-extra": "^11.2.0", "inquirer": "^9.2.12", "ora": "^7.0.1"}, "engines": {"node": ">=18.0.0"}}