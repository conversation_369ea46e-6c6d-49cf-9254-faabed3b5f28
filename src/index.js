#!/usr/bin/env node

import { execa } from 'execa';
import chalk from 'chalk';
import ora from 'ora';
import inquirer from 'inquirer';
import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Importa templates
import { templates } from './templates/index.js';

class MeuAppCreator {
  constructor(projectName) {
    this.projectName = projectName;
    this.projectPath = path.join(process.cwd(), projectName);
  }

  async create() {
    console.log(chalk.blue.bold(`\n🚀 Criando ${this.projectName}...\n`));

    try {
      // 0. Verifica se diretório já existe
      await this.checkProjectDirectory();

      // 1. Cria projeto SvelteKit (interativo)
      await this.createSvelteKit();
      console.log(chalk.green('✓ Projeto SvelteKit criado com sucesso\n'));

      // 2. Adiciona <PERSON>lwind e shadcn
      await this.setupStyling();
      console.log(chalk.green('✓ Tailwind e shadcn configurados\n'));

      // 3. Aguarda e verifica estrutura criada pelo shadcn
      await this.waitForShadcnStructure();

      // 4. Pergunta sobre tracking system
      const trackingEnabled = await this.askAboutTracking();

      // 5. Cria estrutura de arquivos (respeitando o que já existe)
      await this.createFileStructure(trackingEnabled);
      console.log(chalk.green('✓ Estrutura de arquivos criada com sucesso\n'));

      // 6. Instala dependências extras
      await this.installDependencies(trackingEnabled);
      console.log(chalk.green('✓ Dependências extras instaladas\n'));

      // 7. Pergunta e instala componentes shadcn
      console.log(chalk.yellow('🔧 Configurando componentes shadcn...'));
      await this.installShadcnComponents();

      // 8. Limpa e inicia
      await this.finalizeAndStart(trackingEnabled);
    } catch (error) {
      console.error(chalk.red('\n❌ Erro ao criar projeto:'));
      console.error(chalk.red(`Mensagem: ${error.message}`));
      
      if (error.stack) {
        console.error(chalk.gray('\nDetalhes do erro (para debug):'));
        console.error(chalk.gray(error.stack.split('\n').slice(1).join('\n')));
      }
      
      const currentStep = this.getCurrentStep();
      console.error(chalk.yellow(`\nErro ocorreu durante: ${currentStep}`));
      console.error(chalk.yellow('Você pode tentar continuar manualmente a partir deste ponto.'));
      process.exit(1);
    }
  }
  
  getCurrentStep() {
    try {
      // Verifica em qual etapa o processo parou baseado na estrutura existente
      if (!fs.pathExistsSync(this.projectPath)) {
        return 'Criação do projeto SvelteKit';
      }
      
      if (!fs.pathExistsSync(path.join(this.projectPath, 'tailwind.config.js'))) {
        return 'Configuração do Tailwind';
      }
      
      if (!fs.pathExistsSync(path.join(this.projectPath, 'src/lib/components/ui'))) {
        return 'Configuração do shadcn';
      }
      
      if (!fs.pathExistsSync(path.join(this.projectPath, 'src/lib/components/Header.svelte'))) {
        return 'Criação da estrutura de arquivos';
      }
      
      if (!fs.pathExistsSync(path.join(this.projectPath, 'node_modules/pocketbase'))) {
        return 'Instalação das dependências extras';
      }
      
      return 'Instalação dos componentes shadcn';
    } catch (error) {
      return 'Etapa desconhecida';
    }
  }

  async createSvelteKit() {
    console.log(chalk.yellow('📦 Iniciando criação do SvelteKit...\n'));
    
    // Deixa o usuário interagir com a CLI do SvelteKit
    await execa('npx', ['sv', 'create', this.projectName], {
        stdio: 'inherit'
    });
    
    // Entra na pasta do projeto
    process.chdir(this.projectPath);
    
    // Instala dependências
    const spinner = ora('Instalando dependências do SvelteKit...').start();
    await execa('pnpm', ['install'], { stdio: 'pipe' });
    spinner.succeed('Dependências instaladas!');
  }

  async setupStyling() {
    // Adiciona Tailwind
    console.log(chalk.yellow('\n📎 Adicionando Tailwind...\n'));
    console.log(chalk.gray('Se perguntado sobre "Preconditions failed", responda "Yes" para continuar.\n'));

    await execa('npx', ['sv', 'add', 'tailwindcss'], {
        stdio: 'inherit'
    });

    // Inicia shadcn (interativo) - esse continua igual
    console.log(chalk.yellow('\n🎨 Configurando shadcn-svelte...\n'));
    console.log(chalk.gray('Se perguntado sobre configurações, use as opções padrão recomendadas.\n'));

    await execa('npx', ['shadcn-svelte@next', 'init'], {
        stdio: 'inherit'
    });
  }

  async waitForShadcnStructure() {
    const spinner = ora('Verificando estrutura do shadcn...').start();

    // Aguarda a criação das pastas pelo shadcn
    const maxAttempts = 20; // Aumentado para dar mais tempo
    let attempts = 0;

    while (attempts < maxAttempts) {
      const uiPath = path.join(this.projectPath, 'src/lib/components/ui');

      if (await fs.pathExists(uiPath)) {
        // Verifica se há arquivos na pasta também
        const files = await fs.readdir(uiPath).catch(() => []);

        if (files.length > 0) {
          spinner.succeed(`Estrutura shadcn detectada! (${files.length} arquivos encontrados)`);
          console.log(chalk.gray(`Diretório ui criado em: ${uiPath}`));
          return;
        } else {
          spinner.text = 'Pasta ui encontrada, aguardando arquivos...';
        }
      } else {
        spinner.text = `Verificando estrutura shadcn... (tentativa ${attempts + 1}/${maxAttempts})`;
      }

      await new Promise(resolve => setTimeout(resolve, 800)); // Aumentado intervalo
      attempts++;
    }

    spinner.warn('Estrutura shadcn pode não ter sido criada completamente');
    console.log(chalk.yellow('Continuando mesmo assim... Pode ser necessário executar novamente o shadcn manualmente.'));
    console.log(chalk.cyan('  npx shadcn-svelte@next init'));
  }

  async askAboutTracking() {
    console.log(chalk.blue('\n🎯 Sistema de Tracking'));
    console.log(chalk.gray('O sistema de tracking coleta métricas de uso e engajamento dos usuários.'));
    console.log(chalk.gray('Inclui: navegação, tempo de permanência, interações, quality score, etc.\n'));

    const { enableTracking } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'enableTracking',
        message: 'Deseja incluir o sistema de tracking integrado?',
        default: false
      }
    ]);

    if (enableTracking) {
      console.log(chalk.green('✓ Sistema de tracking será incluído'));
      console.log(chalk.gray('  - Módulo completo em src/lib/tracking/'));
      console.log(chalk.gray('  - Layout configurado automaticamente'));
      console.log(chalk.gray('  - Página de exemplos em /tracking'));
      console.log(chalk.gray('  - Dependência PocketBase incluída\n'));
    } else {
      console.log(chalk.yellow('⚠ Sistema de tracking não será incluído\n'));
    }

    return enableTracking;
  }

  async checkProjectDirectory() {
    if (await fs.pathExists(this.projectPath)) {
      console.log(chalk.yellow(`⚠️  Diretório '${this.projectName}' já existe.`));

      const { overwrite } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'overwrite',
          message: 'Deseja sobrescrever o diretório existente?',
          default: false
        }
      ]);

      if (overwrite) {
        console.log(chalk.yellow('🗑️  Removendo diretório existente...'));
        await fs.remove(this.projectPath);
        console.log(chalk.green('✓ Diretório removido\n'));
      } else {
        console.log(chalk.red('❌ Operação cancelada pelo usuário.'));
        process.exit(0);
      }
    }
  }

  async createFileStructure(trackingEnabled = false) {
    const spinner = ora('Criando estrutura de arquivos...').start();

    // Escolhe o layout baseado no tracking
    const layoutTemplate = trackingEnabled ? templates.layoutWithTracking : templates.layout;

    // Estrutura que respeita o que já existe
    const structure = {
      'src/routes': {
        '+layout.svelte': layoutTemplate,
        '+page.svelte': templates.homePage,
        'auth': {
          'login': {
            '+page.svelte': templates.loginPage
          },
          'register': {
            '+page.svelte': templates.registerPage
          }
        }
      },
      'src/lib': {
        'config': {
          'pocketbase.ts': templates.pocketbaseConfig
        },
        'components': {
          'Header.svelte': templates.header,
          'Footer.svelte': templates.footer,
          'ui': {
            'icon.svelte': templates.iconComponent
          }
        },
        'stores': {
          'auth.store.svelte.ts': templates.authStore
        },
        'types': {
          'auth.type.ts': templates.authTypes,
          'global.type.ts': templates.globalTypes
        },
        'utils': {
          'format.utils.ts': templates.formatUtils,
          'date.utils.ts': templates.dateUtils,
          'cn.ts': templates.cnUtil
        }
      },
      '.env.example': templates.envExample,
      '.env': templates.envExample // Cria também o .env local
    };

    // Adiciona página de tracking se habilitado
    if (trackingEnabled) {
      structure['src/routes']['tracking'] = {
        '+page.svelte': templates.trackingPage
      };
    }

    await this.createFiles(structure);

    // Cria estrutura do tracking system se habilitado
    if (trackingEnabled) {
      await this.createTrackingSystem();
    }

    // Atualiza o index.ts do components/ui se existir
    await this.updateComponentsIndex();

    spinner.succeed('Estrutura criada!');
  }

  async createFiles(structure, basePath = '') {
    for (const [name, content] of Object.entries(structure)) {
      const fullPath = path.join(this.projectPath, basePath, name);
      
      if (typeof content === 'string') {
        // Se o arquivo já existe, atualiza-o em vez de ignorar
        if (!await fs.pathExists(fullPath)) {
          await fs.outputFile(fullPath, content);
          console.log(chalk.green(`  Criado: ${name}`));
        } else {
          // Só atualiza layout e pages do SvelteKit
          if (name.includes('+layout') || name.includes('+page')) {
            try {
              await fs.outputFile(fullPath, content);
              console.log(chalk.blue(`  Atualizado: ${name}`));
            } catch (error) {
              console.error(chalk.red(`  Erro ao atualizar ${name}: ${error.message}`));
            }
          } else {
            console.log(chalk.yellow(`  Arquivo já existe (mantido): ${name}`));
          }
        }
      } else {
        // Para diretórios, cria recursivamente
        await this.createFiles(content, path.join(basePath, name));
      }
    }
  }

  async createTrackingSystem() {
    const spinner = ora('Criando sistema de tracking...').start();

    // Estrutura completa do tracking system
    const trackingStructure = {
      'src/lib/tracking': {
        'index.ts': templates.trackingMainIndex,
        'README.md': templates.trackingReadme,
        'CHANGELOG.md': templates.trackingChangelog,
        'types': {
          'tracking.types.ts': templates.trackingTypes
        },
        'core': {
          'tracker.svelte.ts': templates.trackerCore,
          'storage-client.ts': templates.storageClient,
          'session-manager.ts': templates.sessionManager
        },
        'utils': {
          'event-factory.ts': templates.eventFactory
        },
        'config': {
          'page-thresholds.ts': templates.pageThresholds,
          'environments.ts': templates.environments
        }
      }
    };

    await this.createFiles(trackingStructure);
    spinner.succeed('Sistema de tracking criado!');
  }

  async updateComponentsIndex() {
    // Adiciona export do Icon no index.ts do shadcn
    const indexPath = path.join(this.projectPath, 'src/lib/components/ui/index.ts');

    if (await fs.pathExists(indexPath)) {
      const content = await fs.readFile(indexPath, 'utf-8');

      if (!content.includes('icon')) {
        const newContent = content + '\nexport { default as Icon } from "./icon.svelte";\n';
        await fs.writeFile(indexPath, newContent);
      }
    }
  }

  async installDependencies(trackingEnabled = false) {
    const spinner = ora('Instalando dependências extras...').start();

    const deps = [
      '@iconify/svelte',
      'zod', // Para validação
      'svelte-sonner' // Para toasts
    ];

    // Adiciona PocketBase apenas se tracking estiver habilitado
    if (trackingEnabled) {
      deps.push('pocketbase');
      spinner.text = 'Instalando dependências extras (incluindo PocketBase para tracking)...';
    }

    const devDeps = [
      '@iconify/json' // Para autocompletar ícones
    ];

    await execa('pnpm', ['add', ...deps], { stdio: 'pipe' });
    await execa('pnpm', ['add', '-D', ...devDeps], { stdio: 'pipe' });

    if (trackingEnabled) {
      spinner.succeed('Dependências extras instaladas (incluindo PocketBase)!');
    } else {
      spinner.succeed('Dependências extras instaladas!');
    }
  }

  async installShadcnComponents() {
    // Pergunta quais componentes instalar
    const { components } = await inquirer.prompt([
      {
        type: 'checkbox',
        name: 'components',
        message: 'Quais componentes shadcn você quer instalar?',
        choices: [
          { name: 'Button', value: 'button', checked: true },
          { name: 'Input', value: 'input', checked: true },
          { name: 'Label', value: 'label', checked: true },
          { name: 'Card', value: 'card', checked: true },
          { name: 'Dialog', value: 'dialog' },
          { name: 'Select', value: 'select' },
          { name: 'Toast (Sonner)', value: 'sonner', checked: true },
          { name: 'Form', value: 'form' },
          { name: 'Table', value: 'table' },
          { name: 'Tabs', value: 'tabs' },
          { name: 'Dropdown Menu', value: 'dropdown-menu' },
          { name: 'Avatar', value: 'avatar' },
          { name: 'Badge', value: 'badge' },
          { name: 'Skeleton', value: 'skeleton' }
        ]
      }
    ]);
    
    if (components.length > 0) {
      const spinner = ora('Instalando componentes shadcn...').start();
      
      console.log(chalk.blue(`\nComponentes selecionados (${components.length}): ${components.join(' ')}`));
      console.log(chalk.gray(`Comando que será executado: pnpm dlx shadcn-svelte@next add ${components.join(' ')} -y\n`));
      
      try {
        // Usar pnpm dlx para instalação mais rápida
        console.log(chalk.blue('Iniciando instalação com pnpm dlx...'));
        console.time('tempo_instalacao');
        
        // Instalar todos os componentes de uma vez com auto-aprovação (-y)
        spinner.text = `Instalando componentes: ${components.join(', ')}...`;
        
        await execa('pnpm', ['dlx', 'shadcn-svelte@next', 'add', ...components, '-y'], {
          stdio: 'inherit',
          timeout: 180000 // 3 minutos timeout para todos os componentes
        });
        
        console.timeEnd('tempo_instalacao');
        spinner.succeed('Todos os componentes foram instalados com sucesso!');
      } catch (error) {
        spinner.fail('Erro na instalação de componentes');
        console.error(chalk.red(`Detalhes do erro: ${error.message}`));
        
        // Oferecer uma alternativa para instalação manual
        console.log(chalk.yellow('\nVocê pode tentar instalar manualmente depois com:'));
        console.log(chalk.cyan(`pnpm dlx shadcn-svelte@next add ${components.join(' ')} -y`));
        
        // Tentar instalar individualmente como fallback
        const retry = await inquirer.prompt([
          {
            type: 'confirm',
            name: 'installIndividually',
            message: 'Tentar instalar componentes individualmente?',
            default: true
          }
        ]);
        
        if (retry.installIndividually) {
          console.log(chalk.yellow('\nTentando instalar componentes individualmente...'));
          let failedComponents = [];
          
          for (const component of components) {
            try {
              spinner.text = `Instalando componente: ${component}...`;
              await execa('pnpm', ['dlx', 'shadcn-svelte@next', 'add', component, '-y'], {
                stdio: 'inherit',
                timeout: 60000 // 1 minuto timeout por componente
              });
              console.log(chalk.green(`✓ ${component} instalado`));
            } catch (err) {
              failedComponents.push(component);
              console.error(chalk.red(`✗ Falha ao instalar ${component}: ${err.message}`));
            }
          }
          
          if (failedComponents.length > 0) {
            spinner.warn(`${failedComponents.length} componentes não foram instalados.`);
            console.error(chalk.yellow('\nVocê pode instalar manualmente depois com:'));
            console.error(chalk.cyan(`pnpm dlx shadcn-svelte@next add ${failedComponents.join(' ')} -y`));
          } else {
            spinner.succeed('Todos os componentes foram instalados individualmente com sucesso!');
          }
        }
      }
    } else {
      console.log(chalk.yellow('Nenhum componente shadcn selecionado para instalação.'));
    }
  }

  async finalizeAndStart(trackingEnabled = false) {
    // Limpa instalação
    const cleanSpinner = ora('Limpando instalação...').start();
    await execa('pnpm', ['install'], { stdio: 'pipe' });
    cleanSpinner.succeed('Instalação limpa!');

    // Mensagem de sucesso
    console.log(chalk.green.bold('\n✨ Projeto criado com sucesso!\n'));
    console.log(chalk.white('📁 Estrutura criada:'));
    console.log(chalk.gray('  ├── src/routes/ (páginas e layouts)'));
    console.log(chalk.gray('  ├── src/lib/'));
    console.log(chalk.gray('  │   ├── components/ (Header, Footer, ui/)'));
    console.log(chalk.gray('  │   ├── config/ (PocketBase)'));
    console.log(chalk.gray('  │   ├── stores/ (auth)'));
    console.log(chalk.gray('  │   ├── types/ (TypeScript)'));
    console.log(chalk.gray('  │   └── utils/ (formatters, helpers)'));

    if (trackingEnabled) {
      console.log(chalk.gray('  │   └── tracking/ (sistema completo)'));
      console.log(chalk.gray('  └── src/routes/tracking/ (página de exemplos)'));
    }

    console.log(chalk.gray('  └── .env.example (configurações)\n'));
    
    console.log(chalk.white('Para começar:'));
    console.log(chalk.cyan(`  cd ${this.projectName}`));
    console.log(chalk.cyan('  pnpm dev\n'));

    // Informações específicas sobre tracking
    if (trackingEnabled) {
      console.log(chalk.blue('🎯 Sistema de Tracking Integrado:'));
      console.log(chalk.gray('  • Visite /tracking para ver exemplos de uso'));
      console.log(chalk.gray('  • Configure PocketBase em http://localhost:8090'));
      console.log(chalk.gray('  • Documentação completa em src/lib/tracking/README.md'));
      console.log(chalk.gray('  • Layout já configurado automaticamente\n'));
    }

    // Pergunta se quer iniciar agora
    const { startNow } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'startNow',
        message: 'Iniciar o servidor de desenvolvimento agora?',
        default: true
      }
    ]);
    
    if (startNow) {
      console.log(chalk.yellow('\n🚀 Iniciando servidor...\n'));
      console.log(chalk.gray('  Pressione Ctrl+C para parar\n'));
      await execa('pnpm', ['dev'], { stdio: 'inherit' });
    }
  }
}

// CLI principal
const projectName = process.argv[2];

if (!projectName) {
  console.error(chalk.red('Por favor, especifique o nome do projeto:'));
  console.log(chalk.cyan('  create-meu-app <nome-do-projeto>'));
  console.log(chalk.gray('\nExemplo:'));
  console.log(chalk.gray('  create-meu-app meu-saas'));
  process.exit(1);
}

// Valida nome do projeto
if (!/^[a-z0-9-]+$/.test(projectName)) {
  console.error(chalk.red('Nome do projeto deve conter apenas letras minúsculas, números e hífens'));
  process.exit(1);
}

const creator = new MeuAppCreator(projectName);
creator.create().catch(console.error);
