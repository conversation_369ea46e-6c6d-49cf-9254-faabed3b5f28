export const iconComponent = `<script lang="ts">
  import Icon from '@iconify/svelte';
  
  interface Props {
    icon: string;
    size?: string;
    color?: string;
    class?: string;
  }
  
  let { 
    icon, 
    size = '24', 
    color = 'currentColor',
    class: className = ''
  }: Props = $props();
</script>

<Icon 
  {icon} 
  width={size} 
  height={size} 
  {color} 
  class={className}
/>

<!-- 
  Uso: <Icon icon="mdi:home" size="32" />
  
  Biblioteca de ícones: https://icon-sets.iconify.design/
  
  Exemplos de ícones úteis:
  - mdi:home (casa)
  - mdi:account (usuário)
  - mdi:cog (configurações)
  - mdi:logout (sair)
  - mdi:plus (adicionar)
  - mdi:delete (deletar)
  - mdi:pencil (editar)
  - mdi:check (confirmar)
  - mdi:close (fechar)
  - mdi:menu (menu hamburguer)
-->`;
