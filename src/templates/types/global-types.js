export const globalTypes = `// Types globais da aplicação
export interface BaseModel {
  id: string;
  created: string;
  updated: string;
}

export interface ApiError {
  message: string;
  code: number;
  data?: Record<string, any>;
}

export interface PaginatedResponse<T> {
  page: number;
  perPage: number;
  totalItems: number;
  totalPages: number;
  items: T[];
}

// Adicione seus próprios types globais aqui`;
