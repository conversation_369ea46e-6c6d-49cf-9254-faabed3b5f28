export const registerPage = `<script lang="ts">
  import { Button } from '$lib/components/ui/button';
  import { Input } from '$lib/components/ui/input';
  import { Label } from '$lib/components/ui/label';
  import * as Card from '$lib/components/ui/card';
  import { pb } from '$lib/config/pocketbase';
  import { toast } from 'svelte-sonner';
  import { goto } from '$app/navigation';
  
  let email = $state('');
  let password = $state('');
  let passwordConfirm = $state('');
  let loading = $state(false);
  
  async function handleRegister() {
    if (password !== passwordConfirm) {
      toast.error('As senhas não coincidem');
      return;
    }
    
    loading = true;
    
    try {
      await pb.collection('users').create({
        email,
        password,
        passwordConfirm,
        emailVisibility: true
      });
      
      toast.success('Conta criada com sucesso! Faça login para continuar.');
      goto('/auth/login');
    } catch (error: any) {
      toast.error(error.message || 'Erro ao criar conta');
    } finally {
      loading = false;
    }
  }
</script>

<svelte:head>
  <title>Criar Conta | MeuApp</title>
</svelte:head>

<div class="container mx-auto px-4 py-8">
  <div class="max-w-md mx-auto">
    <Card.Root>
      <Card.Header>
        <Card.Title>Criar Conta</Card.Title>
        <Card.Description>
          Crie sua conta para começar
        </Card.Description>
      </Card.Header>
      
      <Card.Content>
        <form onsubmit={(e) => { e.preventDefault(); handleRegister(); }} class="space-y-4">
          <div class="space-y-2">
            <Label for="email">Email</Label>
            <Input
              id="email"
              type="email"
              bind:value={email}
              required
              placeholder="<EMAIL>"
            />
          </div>
          
          <div class="space-y-2">
            <Label for="password">Senha</Label>
            <Input
              id="password"
              type="password"
              bind:value={password}
              required
              minlength="8"
              placeholder="Mínimo 8 caracteres"
            />
          </div>
          
          <div class="space-y-2">
            <Label for="passwordConfirm">Confirmar Senha</Label>
            <Input
              id="passwordConfirm"
              type="password"
              bind:value={passwordConfirm}
              required
              placeholder="Digite a senha novamente"
            />
          </div>
          
          <Button type="submit" class="w-full" disabled={loading}>
            {loading ? 'Criando conta...' : 'Criar conta'}
          </Button>
        </form>
      </Card.Content>
      
      <Card.Footer>
        <p class="text-sm text-center w-full text-muted-foreground">
          Já tem uma conta?{' '}
          <a href="/auth/login" class="text-primary hover:underline">
            Fazer login
          </a>
        </p>
      </Card.Footer>
    </Card.Root>
  </div>
</div>`;
