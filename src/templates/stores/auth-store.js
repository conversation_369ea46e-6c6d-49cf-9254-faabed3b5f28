export const authStore = `import { pb } from '$lib/config/pocketbase';
import { browser } from '$app/environment';
import type { User } from '$lib/types/auth.type';

class AuthState {
  // Estados básicos de autenticação usando $state
  user = $state<User | null>(browser ? (pb.authStore.model as User | null) : null);
  loading = $state(false);
  initialized = $state(false);
  
  // Derived/computed values using $derived
  isAuthenticated = $derived(this.user !== null);
  isLoading = $derived(this.loading);
  isInitialized = $derived(this.initialized);
  
  constructor() {
    // Inicializa o store quando a classe é instanciada (apenas no browser)
    if (browser) {
      this.init();
    }
  }
  
  async init() {
    if (!browser) return;
    
    this.loading = true;
    
    try {
      // Verifica se tem usuário salvo
      if (pb.authStore.isValid) {
        // Atualiza dados do usuário
        const user = await pb.collection('users').authRefresh();
        this.user = user.record as User;
      } else {
        this.user = null;
      }
    } catch (error) {
      // Token inválido
      pb.authStore.clear();
      this.user = null;
    } finally {
      this.loading = false;
      this.initialized = true;
    }
  }
  
  async login(email: string, password: string) {
    this.loading = true;
    
    try {
      const authData = await pb.collection('users').authWithPassword(email, password);
      this.user = authData.record as User;
      return authData;
    } catch (error) {
      this.user = null;
      throw error;
    } finally {
      this.loading = false;
    }
  }
  
  async register(email: string, password: string, passwordConfirm: string) {
    this.loading = true;
    
    try {
      const user = await pb.collection('users').create({
        email,
        password,
        passwordConfirm,
        emailVisibility: true
      });
      
      // Não define o usuário automaticamente após registro
      // O usuário precisa fazer login
      return user;
    } catch (error) {
      throw error;
    } finally {
      this.loading = false;
    }
  }
  
  logout() {
    pb.authStore.clear();
    this.user = null;
    this.loading = false;
  }
}

// Exporta uma instância única do estado de autenticação
export const authStore = new AuthState();`;
