// Localização: create-meu-app/templates/index.js

// ========== IMPORTAÇÕES DOS TEMPLATES SEPARADOS ==========
import { layout } from './layouts/layout.js';
import { layoutWithTracking } from './layouts/layout-with-tracking.js';
import { homePage } from './pages/home-page.js';
import { loginPage } from './pages/login-page.js';
import { registerPage } from './pages/register-page.js';
import { trackingPage } from './pages/tracking-page.js';
import { header } from './components/header.js';
import { footer } from './components/footer.js';
import { iconComponent } from './components/icon-component.js';
import { pocketbaseConfig } from './config/pocketbase-config.js';
import { authStore } from './stores/auth-store.js';
import { authTypes } from './types/auth-types.js';
import { globalTypes } from './types/global-types.js';
import { formatUtils } from './utils/format-utils.js';
import { dateUtils } from './utils/date-utils.js';

// ========== IMPORTAÇÕES DO TRACKING SYSTEM ==========
import { trackingTemplates } from './tracking/index.js';

// ========== TEMPLATES PEQUENOS (permanecem aqui) ==========
export const templates = {
  // Layouts e Páginas (importados)
  layout,
  layoutWithTracking,
  homePage,
  loginPage,
  registerPage,
  trackingPage,

  // Componentes (importados)
  header,
  footer,
  iconComponent,

  // Configurações (importados)
  pocketbaseConfig,

  // Stores (importados)
  authStore,

  // Types (importados)
  authTypes,
  globalTypes,

  // Utils (importados)
  formatUtils,
  dateUtils,

  // Tracking System (importados)
  ...trackingTemplates,

  // ========== TEMPLATES PEQUENOS (ficam aqui) ==========
  cnUtil: `import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}`,

  // ========== ARQUIVO DE AMBIENTE ==========
  envExample: `# URL do PocketBase
VITE_POCKETBASE_URL=http://localhost:8090

# Outras configurações
# VITE_APP_NAME=MeuApp
# VITE_API_TIMEOUT=30000`
};